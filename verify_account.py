#!/usr/bin/env python3
"""
Verify your account configuration and proxy authentication
"""

import json
from pathlib import Path

def verify_account_format():
    """Verify the account format in Accounts.txt"""
    
    print("🔍 ACCOUNT VERIFICATION")
    print("=" * 50)
    
    # Check if Accounts.txt exists
    accounts_file = Path('Accounts.txt')
    if not accounts_file.exists():
        print("❌ Accounts.txt file not found!")
        return False
    
    print("✅ Accounts.txt file found")
    
    # Read and parse accounts
    accounts = []
    with open(accounts_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            print(f"\n📋 Line {line_num}: {line}")
            
            parts = line.split(',')
            
            if len(parts) < 7:
                print(f"❌ Line {line_num}: Not enough fields (found {len(parts)}, need 7)")
                print("   Expected format: email,password,proxy_ip,port,username,password,recovery")
                continue
            
            account = {
                "email": parts[0],
                "password": parts[1], 
                "proxy": parts[2],
                "port": parts[3],
                "proxyUsername": parts[4],
                "proxyPassword": parts[5],
                "recovry": parts[6]
            }
            
            # Verify each field
            print(f"   📧 Email: {account['email']}")
            print(f"   🔒 Password: {'*' * len(account['password'])}")
            print(f"   🌐 Proxy IP: {account['proxy']}")
            print(f"   🔌 Port: {account['port']}")
            print(f"   👤 Proxy Username: {account['proxyUsername']}")
            print(f"   🔑 Proxy Password: {'*' * len(account['proxyPassword'])}")
            print(f"   📮 Recovery: {account['recovry']}")
            
            # Create the magic proxy string
            auth_proxy_string = f"{account['proxyUsername']}:{account['proxyPassword']}@{account['proxy']}:{account['port']}"
            print(f"   ✨ Magic Proxy String: {account['proxyUsername']}:****@{account['proxy']}:{account['port']}")
            
            # Validate fields
            errors = []
            if not account['email'] or '@' not in account['email']:
                errors.append("Invalid email format")
            if not account['password']:
                errors.append("Empty password")
            if not account['proxy']:
                errors.append("Empty proxy IP")
            if not account['port'] or not account['port'].isdigit():
                errors.append("Invalid port")
            if not account['proxyUsername']:
                errors.append("Empty proxy username")
            if not account['proxyPassword']:
                errors.append("Empty proxy password")
            if not account['recovry'] or '@' not in account['recovry']:
                errors.append("Invalid recovery email")
            
            if errors:
                print(f"   ❌ Errors found:")
                for error in errors:
                    print(f"      - {error}")
            else:
                print(f"   ✅ Account format is CORRECT!")
                accounts.append(account)
    
    print(f"\n📊 Summary:")
    print(f"   Total valid accounts: {len(accounts)}")
    
    return len(accounts) > 0

def verify_config():
    """Verify config.json exists"""
    
    print("\n🔧 CONFIG VERIFICATION")
    print("=" * 50)
    
    config_file = Path('config.json')
    if not config_file.exists():
        print("❌ config.json file not found!")
        print("📝 Please create config.json with your automation settings")
        return False
    
    print("✅ config.json file found")
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print("📋 Config contents:")
        for key, value in config.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading config.json: {e}")
        return False

def main():
    """Main verification function"""
    
    print("🔐 GMAIL AUTOMATION VERIFICATION")
    print("=" * 50)
    
    # Verify account format
    accounts_ok = verify_account_format()
    
    # Verify config
    config_ok = verify_config()
    
    print("\n" + "=" * 50)
    print("📋 FINAL VERIFICATION RESULTS:")
    
    if accounts_ok and config_ok:
        print("✅ ALL CHECKS PASSED!")
        print("🚀 Your configuration is ready for proxy authentication")
        print("\n🎯 Next steps:")
        print("   1. Run: python test_proxy_auth.py")
        print("   2. Run: python main.py")
        print("   3. Check for NO browser authentication popups!")
    else:
        print("❌ SOME CHECKS FAILED!")
        if not accounts_ok:
            print("   - Fix your Accounts.txt file")
        if not config_ok:
            print("   - Create or fix your config.json file")
    
    print("=" * 50)

if __name__ == "__main__":
    main()

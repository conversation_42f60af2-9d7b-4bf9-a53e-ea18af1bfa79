#!/usr/bin/env python3
"""
Test script to verify automatic proxy authentication is working
Based on PROXY_AUTHENTICATION_GUIDE.md
"""

import time
from seleniumbase import Driver

def test_proxy_authentication():
    """Test the automatic proxy authentication"""
    
    # Example account data (replace with your actual proxy credentials)
    test_account = {
        'proxy': '*************',
        'port': '3128', 
        'proxyUsername': 'admin123',
        'proxyPassword': 'Hello123'
    }
    
    print("🧪 Testing Automatic Proxy Authentication...")
    print("=" * 50)
    
    try:
        # Step 1: Create magic proxy string (THE SOLUTION!)
        auth_proxy_string = f"{test_account['proxyUsername']}:{test_account['proxyPassword']}@{test_account['proxy']}:{test_account['port']}"
        
        print(f"📋 Proxy format: {test_account['proxyUsername']}:****@{test_account['proxy']}:{test_account['port']}")
        print(f"🔑 Magic string: {auth_proxy_string}")
        
        # Step 2: Create driver with automatic authentication
        print("\n🚀 Creating driver with automatic proxy authentication...")
        
        driver_kwargs = {
            'uc': True,                    # Undetected Chrome
            'headless': False,             # Visible browser
            'proxy': auth_proxy_string,    # 🔑 THE MAGIC LINE!
            'disable_csp': True,
            'disable_ws': True
        }
        
        driver = Driver(**driver_kwargs)
        
        print("✅ Driver created successfully - NO BROWSER POPUPS!")
        
        # Step 3: Test proxy is working
        print("\n🔍 Testing proxy connection...")
        
        # Test with multiple IP checker services
        ip_services = [
            'http://ipinfo.io/json',
            'https://httpbin.org/ip',
            'https://api.ipify.org?format=json'
        ]
        
        proxy_working = False
        
        for service in ip_services:
            try:
                print(f"📡 Checking IP with: {service}")
                driver.get(service)
                time.sleep(3)
                
                page_source = driver.page_source
                
                if test_account['proxy'] in page_source:
                    print(f"✅ SUCCESS: Proxy IP {test_account['proxy']} detected!")
                    print(f"🌐 Your traffic is going through the proxy server")
                    proxy_working = True
                    break
                else:
                    print(f"❌ Proxy IP not detected in response")
                    
            except Exception as e:
                print(f"⚠️  Error testing {service}: {e}")
                continue
        
        if proxy_working:
            print("\n🎉 PROXY AUTHENTICATION TEST: PASSED")
            print("✅ No browser authentication popups")
            print("✅ Automatic credential handling")
            print("✅ Proxy connection verified")
        else:
            print("\n❌ PROXY AUTHENTICATION TEST: FAILED")
            print("❌ Proxy IP not detected - check credentials")
        
        # Step 4: Test browsing with proxy
        print("\n🌐 Testing web browsing through proxy...")
        try:
            driver.get('https://www.google.com')
            time.sleep(3)
            print("✅ Successfully loaded Google through proxy")
        except Exception as e:
            print(f"❌ Error loading Google: {e}")
        
        # Keep browser open for manual inspection
        print("\n⏸️  Browser will stay open for 30 seconds for manual inspection...")
        print("   Check if you see any authentication popups (there should be NONE!)")
        time.sleep(30)
        
        return proxy_working
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")
        return False
        
    finally:
        try:
            driver.quit()
            print("\n🔒 Browser closed")
        except:
            pass

def test_without_credentials():
    """Test what happens without embedded credentials (should show popup)"""
    
    print("\n" + "=" * 50)
    print("🧪 Testing WITHOUT embedded credentials (should fail)...")
    
    try:
        # This should cause authentication popup
        driver_kwargs = {
            'uc': True,
            'headless': False,
            'proxy': '*************:3128',  # No credentials = popup!
            'disable_csp': True,
            'disable_ws': True
        }
        
        print("⚠️  Creating driver WITHOUT embedded credentials...")
        print("   (This should show browser authentication popup)")
        
        driver = Driver(**driver_kwargs)
        
        print("❌ This should have failed or shown popup!")
        time.sleep(10)
        
        driver.quit()
        
    except Exception as e:
        print(f"✅ Expected error (no credentials): {e}")

if __name__ == "__main__":
    print("🔐 PROXY AUTHENTICATION TEST SUITE")
    print("Based on PROXY_AUTHENTICATION_GUIDE.md")
    print("=" * 50)
    
    # Test 1: With embedded credentials (should work)
    success = test_proxy_authentication()
    
    # Test 2: Without credentials (should fail/popup)
    # Uncomment to test failure case:
    # test_without_credentials()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 OVERALL RESULT: PROXY AUTHENTICATION WORKING!")
        print("✅ Copy the create_driver method to your main script")
    else:
        print("❌ OVERALL RESULT: PROXY AUTHENTICATION FAILED")
        print("🔧 Check your proxy credentials and server")
    
    print("=" * 50)

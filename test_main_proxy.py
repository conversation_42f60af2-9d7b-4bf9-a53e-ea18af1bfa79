#!/usr/bin/env python3
"""
Test the exact same proxy configuration as main.py
"""

import time
from pathlib import Path
from seleniumbase import Driver

def test_main_proxy_config():
    """Test the exact same configuration as main.py"""
    
    print("🧪 Testing MAIN.PY Proxy Configuration")
    print("=" * 50)
    
    # Load account from Accounts.txt (same as main.py)
    try:
        with open('Accounts.txt', 'r') as f:
            line = f.readline().strip()
            parts = line.split(',')
            account = {
                'email': parts[0],
                'password': parts[1], 
                'proxy': parts[2],
                'port': parts[3],
                'proxyUsername': parts[4],
                'proxyPassword': parts[5],
                'recovry': parts[6]
            }
    except Exception as e:
        print(f"❌ Error loading account: {e}")
        return False
    
    print(f"📧 Testing account: {account['email']}")
    
    try:
        # EXACT SAME CODE AS MAIN.PY create_driver method
        print("\n🔧 Creating driver with EXACT main.py configuration...")
        
        # Step 1: Create magic proxy string (EXACTLY as in main.py)
        auth_proxy_string = None
        if (account.get('proxyUsername') and account.get('proxyPassword') and 
            account.get('proxy') and account.get('port')):
            
            # THE MAGIC: Embed credentials in proxy URL
            auth_proxy_string = f"{account['proxyUsername']}:{account['proxyPassword']}@{account['proxy']}:{account['port']}"
            
            print(f"🔑 Proxy string: {auth_proxy_string}")
        
        # Step 2: SeleniumBase configuration (EXACTLY as in main.py)
        driver_kwargs = {
            'uc': True,                    # Undetected Chrome
            'headless': False,             # Visible browser
            'disable_csp': True,
            'disable_ws': True
            # NO user_data_dir - this was the fix!
        }
        
        # Step 3: Add proxy with embedded credentials (THE MAGIC LINE!)
        if auth_proxy_string:
            driver_kwargs['proxy'] = auth_proxy_string
        
        print(f"📋 Driver kwargs: {driver_kwargs}")
        
        # Step 4: Create driver - NO POPUPS!
        print("\n🚀 Creating driver...")
        driver = Driver(**driver_kwargs)
        
        print("✅ Driver created successfully!")
        print("🔍 Check browser window - should be NO authentication popup!")
        
        # Test proxy is working
        print("\n🌐 Testing proxy connection...")
        driver.get('http://ipinfo.io/json')
        time.sleep(3)
        
        page_source = driver.page_source
        if account['proxy'] in page_source:
            print(f"✅ SUCCESS: Proxy IP {account['proxy']} detected!")
            print("🎉 PROXY AUTHENTICATION WORKING IN MAIN.PY CONFIG!")
        else:
            print(f"❌ Proxy IP not detected")
            print("🔧 Check if browser showed authentication popup")
        
        # Test Gmail access
        print("\n📧 Testing Gmail access...")
        try:
            driver.get('https://accounts.google.com/signin')
            time.sleep(5)
            print("✅ Gmail login page loaded through proxy")
        except Exception as e:
            print(f"❌ Error accessing Gmail: {e}")
        
        # Keep browser open for inspection
        print("\n⏸️  Browser staying open for 30 seconds...")
        print("   👀 MANUALLY CHECK: Are there any authentication popups?")
        print("   ✅ Expected: NO popups, automatic proxy connection")
        time.sleep(30)
        
        driver.quit()
        print("\n🔒 Browser closed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_main_proxy_config()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 MAIN.PY PROXY CONFIG TEST: PASSED")
        print("✅ The fix should work in main.py")
    else:
        print("❌ MAIN.PY PROXY CONFIG TEST: FAILED")
        print("🔧 Need to investigate further")
    print("=" * 50)

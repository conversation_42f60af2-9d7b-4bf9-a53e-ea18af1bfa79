#!/usr/bin/env python3
"""
Test the proxy authentication fix in 2.py
"""

import time
import sys
from pathlib import Path

# Add the current directory to Python path to import from 2.py
sys.path.insert(0, str(Path(__file__).parent))

def test_2py_proxy():
    """Test the proxy authentication in 2.py"""
    
    print("🧪 Testing 2.PY Proxy Authentication Fix")
    print("=" * 50)
    
    try:
        # Import the GmailAutomation class from 2.py
        from importlib.util import spec_from_file_location, module_from_spec
        
        spec = spec_from_file_location("gmail_automation_2", "2.py")
        module = module_from_spec(spec)
        spec.loader.exec_module(module)
        
        GmailAutomation = module.GmailAutomation
        
        print("✅ Successfully imported GmailAutomation from 2.py")
        
    except Exception as e:
        print(f"❌ Error importing from 2.py: {e}")
        return False
    
    # Load account from Accounts.txt
    try:
        with open('Accounts.txt', 'r') as f:
            line = f.readline().strip()
            parts = line.split(',')
            account = {
                'email': parts[0],
                'password': parts[1], 
                'proxy': parts[2],
                'port': parts[3],
                'proxyUsername': parts[4],
                'proxyPassword': parts[5],
                'recovry': parts[6]
            }
    except Exception as e:
        print(f"❌ Error loading account: {e}")
        return False
    
    print(f"📧 Testing account: {account['email']}")
    
    try:
        # Create automation instance
        automation = GmailAutomation(Path.cwd())
        
        print("\n🔧 Creating driver with 2.py configuration...")
        
        # Test the create_driver method from 2.py
        driver = automation.create_driver(account)
        
        if not driver:
            print("❌ Failed to create driver")
            return False
        
        print("✅ Driver created successfully!")
        print("🔍 Check browser window - should be NO authentication popup!")
        
        # Test proxy is working
        print("\n🌐 Testing proxy connection...")
        driver.get('http://ipinfo.io/json')
        time.sleep(3)
        
        page_source = driver.page_source
        if account['proxy'] in page_source:
            print(f"✅ SUCCESS: Proxy IP {account['proxy']} detected!")
            print("🎉 PROXY AUTHENTICATION WORKING IN 2.PY!")
        else:
            print(f"❌ Proxy IP not detected")
            print("🔧 Check if browser showed authentication popup")
        
        # Test Gmail access
        print("\n📧 Testing Gmail access...")
        try:
            driver.get('https://accounts.google.com/signin')
            time.sleep(5)
            print("✅ Gmail login page loaded through proxy")
        except Exception as e:
            print(f"❌ Error accessing Gmail: {e}")
        
        # Keep browser open for inspection
        print("\n⏸️  Browser staying open for 20 seconds...")
        print("   👀 MANUALLY CHECK: Are there any authentication popups?")
        print("   ✅ Expected: NO popups, automatic proxy connection")
        time.sleep(20)
        
        driver.quit()
        print("\n🔒 Browser closed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_2py_proxy()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 2.PY PROXY AUTHENTICATION TEST: PASSED")
        print("✅ The fix is working in 2.py")
        print("🚀 Both main.py and 2.py now have automatic proxy authentication!")
    else:
        print("❌ 2.PY PROXY AUTHENTICATION TEST: FAILED")
        print("🔧 Need to investigate further")
    print("=" * 50)

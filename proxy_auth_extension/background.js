// Enhanced background script for automatic proxy authentication
let authAttempts = 0;
const MAX_AUTH_ATTEMPTS = 5;

// Store credentials from storage or default
let proxyCredentials = {
    username: '',
    password: ''
};

// Load credentials from Chrome storage
chrome.storage.local.get(['proxyUsername', 'proxyPassword'], function(result) {
    if (result.proxyUsername && result.proxyPassword) {
        proxyCredentials.username = result.proxyUsername;
        proxyCredentials.password = result.proxyPassword;
        console.log('Loaded proxy credentials from storage');
    }
});

// Handle authentication requests with enhanced logic
chrome.webRequest.onAuthRequired.addListener(
    function(details) {
        console.log('Proxy auth requested for:', details.url);
        console.log('Auth attempt:', authAttempts + 1);
        
        authAttempts++;
        
        if (authAttempts > MAX_AUTH_ATTEMPTS) {
            console.log('Max auth attempts reached, cancelling');
            authAttempts = 0; // Reset for next request
            return {cancel: true};
        }
        
        // Try stored credentials first
        if (proxyCredentials.username && proxyCredentials.password) {
            console.log('Using stored proxy credentials');
            return {
                authCredentials: {
                    username: proxyCredentials.username,
                    password: proxyCredentials.password
                }
            };
        }
        
        // Fallback: Return empty credentials to bypass
        console.log('Using empty credentials bypass');
        return {
            authCredentials: {
                username: '',
                password: ''
            }
        };
    },
    {urls: ["<all_urls>"]},
    ["asyncBlocking"]
);

// Reset auth attempts periodically
setInterval(() => {
    authAttempts = 0;
}, 30000);

// Listen for credential updates from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'updateCredentials') {
        proxyCredentials.username = message.username;
        proxyCredentials.password = message.password;
        
        // Store in Chrome storage
        chrome.storage.local.set({
            proxyUsername: message.username,
            proxyPassword: message.password
        });
        
        console.log('Updated proxy credentials');
        sendResponse({success: true});
    }
});

console.log('Proxy Auth Handler extension loaded successfully');
